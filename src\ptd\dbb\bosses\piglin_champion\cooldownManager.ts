import { Entity, system } from "@minecraft/server";

/**
 * Cooldown durations for each attack type (in ticks)
 * These match the cooldown times used in the original timer-based system
 */
export const ATTACK_COOLDOWNS = {
  horizontal: 60,
  vertical: 60,
  foot_stomp: 60,
  spin_slam: 60,
  body_slam: 60,
  upchuck: 60,
  charging: 120,
  summoning_chant: 60,
  healing: 60,
  stunned_standing: 60,
  stunned_sitting: 60,
} as const;

/**
 * Schedules a cooldown for the piglin champion after an attack
 * This replaces the timer-based cooldown system with an event-based one
 * 
 * @param piglinChampion The piglin champion entity
 * @param attackType The type of attack that was performed
 */
export function scheduleCooldown(piglinChampion: Entity, attackType: string): void {
  const cooldownTime = ATTACK_COOLDOWNS[attackType as keyof typeof ATTACK_COOLDOWNS] || 60;
  
  // Schedule the cooldown to end after the specified time
  const cooldownTimeout = system.runTimeout(() => {
    try {
      const isDead = piglinChampion.getProperty("ptd_dbb:dead") as boolean;
      if (isDead) {
        system.clearRun(cooldownTimeout);
        return;
      }
      
      // Set cooling_down to false to allow the next attack
      piglinChampion.setProperty("ptd_dbb:cooling_down", false);
      system.clearRun(cooldownTimeout);
    } catch (error) {
      // Entity might have been removed
      system.clearRun(cooldownTimeout);
    }
  }, cooldownTime);
}
