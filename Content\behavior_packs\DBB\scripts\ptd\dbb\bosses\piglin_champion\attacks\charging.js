import { Entity<PERSON><PERSON>ge<PERSON><PERSON><PERSON>, GameMode, Player, system } from "@minecraft/server";
import { getDistance } from "../../../utilities/vector3";
import { PIGLIN_CHAMPION_ATTACK_DAMAGES } from "../../general_attacks/attackDamages";
/**
 * Starts continuous damage application during the charging movement
 * Applies damage to entities as the piglin charges through them from tick 46 to 90
 *
 * @param piglinChampion The piglin champion entity
 */
export async function startContinuousChargingDamage(piglinChampion) {
    // Get the current attack timer
    let attackTimer = piglinChampion.getProperty("ptd_dbb:attack_timer");
    // Wait until tick 46 (start of charge_2 animation) before starting continuous damage
    while (attackTimer < 46) {
        await system.waitTicks(1);
        try {
            // Update attack timer
            attackTimer = piglinChampion.getProperty("ptd_dbb:attack_timer");
        }
        catch (e) {
            // Entity might have been removed
            return;
        }
    }
    // Apply damage every 4 ticks during the charging movement (ticks 46-90)
    const damageInterval = system.runInterval(() => {
        try {
            // Check if the piglin is still charging and within the damage window
            const currentTimer = piglinChampion.getProperty("ptd_dbb:attack_timer");
            const currentAttack = piglinChampion.getProperty("ptd_dbb:attack");
            // Stop if attack changed or we've reached tick 90 (when slowness is applied)
            if (currentAttack !== "charging" || currentTimer >= 90) {
                system.clearRun(damageInterval);
                return;
            }
            // Apply damage to entities during the charge
            applyChargingDamage(piglinChampion, true); // true = apply knockback during continuous damage
        }
        catch (e) {
            // Entity might have been removed
            system.clearRun(damageInterval);
        }
    }, 4); // Apply damage every 4 ticks for balanced continuous damage
}
/**
 * Executes the final charging attack impact for the Piglin Champion
 * Applies damage and knockback to nearby entities when the piglin charges into them
 * This is the final impact at tick 96
 *
 * @param piglinChampion The piglin champion entity
 */
export function executeChargingAttack(piglinChampion) {
    // Apply final impact damage with knockback
    applyChargingDamage(piglinChampion, true); // true = apply knockback for final impact
}
/**
 * Applies charging damage to nearby entities
 *
 * @param piglinChampion The piglin champion entity
 * @param applyKnockback Whether to apply knockback (true for final impact, false for continuous damage)
 */
function applyChargingDamage(piglinChampion, applyKnockback) {
    // Apply damage to nearby entities
    const damageRadius = 4;
    // Use direct damage value instead of percentage
    const damage = PIGLIN_CHAMPION_ATTACK_DAMAGES.charging.damage;
    // Get direction vector directly from the piglin's view direction
    const viewDirection = piglinChampion.getViewDirection();
    const dirX = viewDirection.x;
    const dirZ = viewDirection.z;
    // Calculate position 3 blocks in front of the piglin
    const originPos = {
        x: piglinChampion.location.x + dirX * 3,
        y: piglinChampion.location.y,
        z: piglinChampion.location.z + dirZ * 3
    };
    // Apply damage to nearby entities
    piglinChampion.dimension
        .getEntities({
        location: originPos,
        maxDistance: damageRadius,
        excludeTypes: ["minecraft:xp_orb", "minecraft:item"],
        excludeFamilies: ["piglin_champion", "piglin", "rock"]
    })
        .forEach((entity) => {
        // Apply damage
        entity.applyDamage(damage, { cause: EntityDamageCause.entityAttack, damagingEntity: piglinChampion });
        // Only apply knockback if requested (for final impact)
        if (applyKnockback) {
            // Use piglin's direction for knockback
            // Create 2D points (same y-coordinate) to calculate horizontal distance
            const point1 = { x: entity.location.x, y: 0, z: entity.location.z };
            const point2 = { x: originPos.x, y: 0, z: originPos.z };
            const distance = getDistance(point1, point2);
            if (distance > 0) {
                // Use the piglin's direction for knockback
                const nx = dirX;
                const nz = dirZ;
                // Charging attack parameters
                const horizontalStrength = 6.0; // Knock players back 6 blocks
                const verticalStrength = 0.8;
                try {
                    // Try to apply knockback first
                    if (entity instanceof Player) {
                        const gameMode = entity.getGameMode();
                        if (gameMode === GameMode.survival || gameMode === GameMode.adventure) {
                            entity.applyKnockback(nx, nz, horizontalStrength, verticalStrength);
                        }
                    }
                    else {
                        entity.applyKnockback(nx, nz, horizontalStrength, verticalStrength);
                    }
                }
                catch (e) {
                    // Fallback to applyImpulse if applyKnockback fails
                    const impulse = {
                        x: nx * horizontalStrength,
                        y: verticalStrength,
                        z: nz * horizontalStrength
                    };
                    entity.applyImpulse(impulse);
                }
            }
        }
    });
}
