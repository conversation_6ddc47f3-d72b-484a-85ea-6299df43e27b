import { system, EntityDamageCause } from "@minecraft/server";
import { filterValidTargets } from "../../general_mechanics/targetUtils";
import { GRIMHOWL_ATTACK_DAMAGES } from "../../general_attacks/attackDamages";
export function doGrimhowlSlash(sourceEntity) {
    try {
        if (!sourceEntity)
            return;
        const isEnraged = sourceEntity.getProperty('ptd_dbb:enraged');
        sourceEntity.triggerEvent(isEnraged ? 'ptd_dbb:grimhowl_slash_enraged' : 'ptd_dbb:grimhowl_slash');
        const nearbyTargets = sourceEntity.dimension.getEntities({
            location: sourceEntity.location,
            excludeTypes: ["minecraft:item", "minecraft:xp_orb"],
            maxDistance: 16
        }).filter(filterValidTargets(sourceEntity));
        if (nearbyTargets.length === 0) {
            sourceEntity.triggerEvent('ptd_dbb:attack_done');
            return;
        }
        sourceEntity.teleport(sourceEntity.location, { facingLocation: nearbyTargets[0].location, keepVelocity: true });
        sourceEntity.runCommand(`playsound mob.ptd_dbb_grimhowl.grunt @a ~ ~ ~ 5${1 + (Math.random() * 0.3)}`);
        system.runTimeout(() => {
            if (!sourceEntity)
                return;
            const viewDirection = sourceEntity.getViewDirection();
            const impulse = {
                x: viewDirection.x * (sourceEntity.getProperty('ptd_dbb:sword_mode') ? 2.5 : 4),
                y: 0.1,
                z: viewDirection.z * (sourceEntity.getProperty('ptd_dbb:sword_mode') ? 2.5 : 4)
            };
            sourceEntity.applyImpulse(impulse);
            const collideTargets = sourceEntity.dimension.getEntities({
                location: sourceEntity.location,
                excludeTypes: ["minecraft:item", "minecraft:xp_orb"],
                maxDistance: 4.4
            }).filter(filterValidTargets(sourceEntity));
            collideTargets.forEach((entity) => {
                entity.applyDamage(GRIMHOWL_ATTACK_DAMAGES.collision_damage.damage, { damagingEntity: sourceEntity, cause: EntityDamageCause.entityAttack });
            });
        }, isEnraged ? 5 : 10);
        system.runTimeout(() => {
            if (!sourceEntity)
                return;
            if (sourceEntity.getProperty('ptd_dbb:sword_mode')) {
                sourceEntity.runCommand(`playsound mob.ptd_dbb_grimhowl.sword_swipe @a ~ ~ ~ 10 ${1 + (Math.random() * 0.3)}`);
            }
            else {
                sourceEntity.runCommand(`playsound mob.ptd_dbb_grimhowl.grunt @a ~ ~ ~ 10 ${1 + (Math.random() * 0.3)}`);
                sourceEntity.runCommand(`playsound mob.ravager.bite @a ~ ~ ~ 10 ${2 + (Math.random() * 0.3)}`);
            }
            const viewDirection = sourceEntity.getViewDirection();
            const forwardLocation = {
                x: sourceEntity.location.x + viewDirection.x * 5,
                y: sourceEntity.location.y,
                z: sourceEntity.location.z + viewDirection.z * 5
            };
            const collideTargets = sourceEntity.dimension.getEntities({
                location: sourceEntity.location,
                excludeTypes: ["minecraft:item", "minecraft:xp_orb"],
                maxDistance: 4.4
            }).filter(filterValidTargets(sourceEntity));
            const frontTargets = sourceEntity.dimension.getEntities({
                location: forwardLocation,
                excludeTypes: ["minecraft:item", "minecraft:xp_orb"],
                maxDistance: (sourceEntity.getProperty('ptd_dbb:sword_mode') ? 5 : 2.5)
            }).filter(filterValidTargets(sourceEntity));
            collideTargets.forEach((entity) => {
                entity.applyDamage(GRIMHOWL_ATTACK_DAMAGES.collision_damage.damage, { damagingEntity: sourceEntity, cause: EntityDamageCause.entityAttack });
            });
            frontTargets.forEach((entity) => {
                entity.applyDamage(GRIMHOWL_ATTACK_DAMAGES.slash.damage, { damagingEntity: sourceEntity, cause: EntityDamageCause.entityAttack });
            });
        }, isEnraged ? 10 : 20);
    }
    catch (error) {
        sourceEntity?.triggerEvent('ptd_dbb:attack_done');
    }
}
